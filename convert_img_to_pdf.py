#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片转PDF工具
"""

from PIL import Image
import os

def convert_image_to_pdf(image_path, output_path):
    """将图片转换为PDF"""
    try:
        # 打开图片
        image = Image.open(image_path)
        
        # 如果图片是RGBA模式，转换为RGB
        if image.mode == 'RGBA':
            image = image.convert('RGB')
        
        # 保存为PDF
        image.save(output_path, 'PDF', resolution=100.0)
        print(f"成功将 {image_path} 转换为 {output_path}")
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

if __name__ == "__main__":
    # 输入图片路径
    image_file = "IMG_20250725_000016.jpg"
    output_file = "IMG_20250725_000016.pdf"
    
    # 检查文件是否存在
    if not os.path.exists(image_file):
        print(f"错误: 找不到文件 {image_file}")
        exit(1)
    
    # 转换图片为PDF
    if convert_image_to_pdf(image_file, output_file):
        print(f"PDF文件已保存为: {output_file}")
    else:
        print("转换失败")
